import { <PERSON>, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { toast } from "sonner";

const ClientVendorManagementPage = () => {
  const mockVendors = [
    { id: "v001", name: "Rapid Plumbers", email: "<EMAIL>", status: "Active", services: "Plumbing" },
    { id: "v002", name: "Brush Strokes Pro", email: "<EMAIL>", status: "Active", services: "Painting" },
    { id: "v003", name: "Certified Inspectors Inc.", email: "<EMAIL>", status: "Active", services: "Inspections" },
    { id: "v004", name: "Green Thumb Landscaping", email: "<EMAIL>", status: "Active", services: "Landscaping" },
    { id: "v005", name: "Sparky Electric", email: "<EMAIL>", status: "Suspended", services: "Electrical" },
  ];

  const mockPendingVendors = [
    { id: "p001", name: "Clean Sweep Services", email: "<EMAIL>", services: "Cleaning" },
    { id: "p002", name: "Move It Right", email: "<EMAIL>", services: "Moving" },
  ];

  const handleAddVendor = () => {
    toast.info("Opening form to add new vendor...");
  };

  const handleViewVendor = (vendorName: string) => {
    toast.info(`Viewing details for ${vendorName}...`);
  };

  const handleEditVendor = (vendorName: string) => {
    toast.info(`Editing ${vendorName}...`);
  };

  const handleApproveVendor = (vendorName: string) => {
    toast.success(`Approved ${vendorName}.`);
  };

  const handleRejectVendor = (vendorName: string) => {
    toast.error(`Rejected ${vendorName}.`);
  };

  const handleReviewAllApplications = () => {
    toast.info("Reviewing all pending applications...");
  };

  return (
    <div className="p-6">
      <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-6">Manage Vendors</h2>
      <p className="text-lg text-gray-700 dark:text-gray-300 mb-8">
        Review and manage the service providers in your marketplace.
      </p>

      <Card className="mb-8">
        <CardHeader>
          <CardTitle>Vendor List</CardTitle>
          <CardDescription>View and search your current vendors.</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="mb-4">
            <Input placeholder="Search vendors by name or email..." />
          </div>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Vendor Name</TableHead>
                  <TableHead>Email</TableHead>
                  <TableHead>Services</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {mockVendors.map((vendor) => (
                  <TableRow key={vendor.id}>
                    <TableCell className="font-medium">{vendor.name}</TableCell>
                    <TableCell>{vendor.email}</TableCell>
                    <TableCell>{vendor.services}</TableCell>
                    <TableCell>
                      <Badge variant={vendor.status === "Active" ? "default" : "destructive"}>
                        {vendor.status}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-right">
                      <Button onClick={() => handleViewVendor(vendor.name)} variant="ghost" size="sm">View</Button>
                      <Button onClick={() => handleEditVendor(vendor.name)} variant="ghost" size="sm">Edit</Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
          <Button onClick={handleAddVendor} variant="outline" className="mt-4">Add New Vendor</Button>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Pending Approvals</CardTitle>
          <CardDescription>Review new vendor applications.</CardDescription>
        </CardHeader>
        <CardContent>
          {mockPendingVendors.length > 0 ? (
            <div className="space-y-4">
              {mockPendingVendors.map((vendor) => (
                <div key={vendor.id} className="flex flex-col sm:flex-row items-start sm:items-center justify-between p-3 border rounded-md dark:border-gray-700">
                  <div>
                    <p className="font-medium">{vendor.name}</p>
                    <p className="text-sm text-gray-500 dark:text-gray-400">{vendor.email} - {vendor.services}</p>
                  </div>
                  <div className="space-x-2 mt-2 sm:mt-0">
                    <Button onClick={() => handleApproveVendor(vendor.name)} variant="default" size="sm">Approve</Button>
                    <Button onClick={() => handleRejectVendor(vendor.name)} variant="outline" size="sm">Reject</Button>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <p className="text-gray-600 dark:text-gray-400">No pending vendor applications at this time.</p>
          )}
          <Button onClick={handleReviewAllApplications} variant="outline" className="mt-4">Review All Applications</Button>
        </CardContent>
      </Card>
    </div>
  );
};

export default ClientVendorManagementPage;